/**
 * 批次节点注册表测试
 * 验证改进后的注册表功能
 */

import { Debug } from '../../utils/Debug';
import { 
  batchNodeRegistry,
  initializeBatchNodeRegistry,
  getBatchNodeStatistics,
  getRegistrationErrors,
  isInitialized
} from './NodeRegistry';
import {
  initializeCompleteNodeRegistry,
  getIntegrationStatus,
  getDetailedIntegrationReport,
  batchNodeRegistryIntegration
} from './BatchNodeRegistryIntegration';

/**
 * 测试批次节点注册表基本功能
 */
async function testBatchNodeRegistry() {
  console.log('\n=== 测试批次节点注册表 ===');
  
  try {
    // 测试初始化前状态
    console.log('初始化前状态:', isInitialized());
    
    // 初始化批次注册表
    console.log('开始初始化批次节点注册表...');
    await initializeBatchNodeRegistry();
    
    // 检查初始化状态
    console.log('初始化后状态:', isInitialized());
    
    // 获取注册错误
    const errors = getRegistrationErrors();
    if (errors.length > 0) {
      console.warn('注册错误:', errors);
    } else {
      console.log('✓ 无注册错误');
    }
    
    // 获取统计信息
    const stats = getBatchNodeStatistics();
    console.log('批次节点统计:', {
      totalNodes: stats.totalNodes,
      categories: stats.categories,
      registeredAt: stats.registeredAt
    });
    
    // 测试节点创建
    const nodeTypes = batchNodeRegistry.getRegisteredNodeTypes();
    console.log(`✓ 已注册 ${nodeTypes.length} 个节点类型`);
    
    if (nodeTypes.length > 0) {
      const firstNodeType = nodeTypes[0];
      const nodeInfo = batchNodeRegistry.getNodeInfo(firstNodeType);
      console.log('示例节点信息:', nodeInfo);
    }
    
    console.log('✓ 批次节点注册表测试通过');
    
  } catch (error) {
    console.error('✗ 批次节点注册表测试失败:', error);
  }
}

/**
 * 测试完整集成功能
 */
async function testCompleteIntegration() {
  console.log('\n=== 测试完整集成功能 ===');
  
  try {
    // 完整初始化
    console.log('开始完整系统初始化...');
    const status = await initializeCompleteNodeRegistry();
    
    console.log('集成状态:', {
      batchRegistryInitialized: status.batchRegistryInitialized,
      systemRegistryInitialized: status.systemRegistryInitialized,
      totalNodes: status.totalNodes,
      batchNodes: status.batchNodes,
      systemNodes: status.systemNodes,
      errors: status.errors.length,
      warnings: status.warnings.length
    });
    
    // 检查集成是否成功
    const isSuccessful = batchNodeRegistryIntegration.isIntegratedSuccessfully();
    console.log('集成是否成功:', isSuccessful);
    
    // 获取详细报告
    const report = getDetailedIntegrationReport();
    console.log('系统健康状态:', report.summary.isHealthy);
    console.log('节点覆盖情况:', report.summary.nodesCoverage);
    
    if (status.errors.length > 0) {
      console.warn('集成错误:', status.errors);
    }
    
    if (status.warnings.length > 0) {
      console.warn('集成警告:', status.warnings.slice(0, 5)); // 只显示前5个警告
    }
    
    console.log('✓ 完整集成测试通过');
    
  } catch (error) {
    console.error('✗ 完整集成测试失败:', error);
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===');
  
  try {
    // 测试重复初始化
    console.log('测试重复初始化...');
    await initializeBatchNodeRegistry();
    await initializeBatchNodeRegistry(); // 应该不会出错
    console.log('✓ 重复初始化处理正确');
    
    // 测试错误清理
    const initialErrors = getRegistrationErrors();
    batchNodeRegistry.clearRegistrationErrors();
    const clearedErrors = getRegistrationErrors();
    
    console.log('错误清理测试:', {
      初始错误数: initialErrors.length,
      清理后错误数: clearedErrors.length
    });
    
    console.log('✓ 错误处理测试通过');
    
  } catch (error) {
    console.error('✗ 错误处理测试失败:', error);
  }
}

/**
 * 测试热重载功能
 */
async function testHotReload() {
  console.log('\n=== 测试热重载功能 ===');
  
  try {
    console.log('测试热重载...');
    const reloadStatus = await batchNodeRegistryIntegration.reloadIntegration();
    
    console.log('重载后状态:', {
      batchRegistryInitialized: reloadStatus.batchRegistryInitialized,
      systemRegistryInitialized: reloadStatus.systemRegistryInitialized,
      totalNodes: reloadStatus.totalNodes,
      errors: reloadStatus.errors.length
    });
    
    console.log('✓ 热重载测试通过');
    
  } catch (error) {
    console.error('✗ 热重载测试失败:', error);
  }
}

/**
 * 性能测试
 */
async function testPerformance() {
  console.log('\n=== 性能测试 ===');
  
  try {
    const startTime = Date.now();
    
    // 重新初始化以测试性能
    await batchNodeRegistryIntegration.reloadIntegration();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`初始化耗时: ${duration}ms`);
    
    // 测试节点查找性能
    const nodeTypes = batchNodeRegistry.getRegisteredNodeTypes();
    const lookupStartTime = Date.now();
    
    for (let i = 0; i < 100; i++) {
      for (const nodeType of nodeTypes) {
        batchNodeRegistry.getNodeInfo(nodeType);
      }
    }
    
    const lookupEndTime = Date.now();
    const lookupDuration = lookupEndTime - lookupStartTime;
    
    console.log(`节点查找性能 (${nodeTypes.length * 100} 次查找): ${lookupDuration}ms`);
    console.log('✓ 性能测试通过');
    
  } catch (error) {
    console.error('✗ 性能测试失败:', error);
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('开始批次节点注册表改进验证测试...');
  
  // 设置调试级别
  Debug.setLevel('info');
  
  try {
    await testBatchNodeRegistry();
    await testCompleteIntegration();
    await testErrorHandling();
    await testHotReload();
    await testPerformance();
    
    console.log('\n🎉 所有测试通过！批次节点注册表改进成功！');
    
    // 输出最终统计
    const finalReport = getDetailedIntegrationReport();
    console.log('\n=== 最终系统状态 ===');
    console.log('系统健康:', finalReport.summary.isHealthy ? '✓ 健康' : '✗ 异常');
    console.log('总节点数:', finalReport.summary.nodesCoverage.total);
    console.log('系统节点数:', finalReport.summary.nodesCoverage.system);
    console.log('批次节点数:', finalReport.summary.nodesCoverage.batch);
    console.log('总错误数:', finalReport.summary.totalErrors);
    console.log('总警告数:', finalReport.summary.totalWarnings);
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error);
  }
}

/**
 * 如果直接运行此文件，执行所有测试
 */
if (require.main === module) {
  runAllTests().catch(console.error);
}
