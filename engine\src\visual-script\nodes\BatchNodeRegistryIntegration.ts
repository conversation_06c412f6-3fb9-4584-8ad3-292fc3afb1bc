/**
 * 批次节点注册表集成模块
 * 提供统一的初始化和集成接口
 * 
 * 改进特性：
 * - 与系统注册表的无缝集成
 * - 完善的错误处理和验证
 * - 详细的统计和监控信息
 * - 支持热重载和开发时调试
 */

import { Debug } from '../../utils/Debug';
import { 
  initializeVisualScriptSystem,
  NodeRegistry as SystemNodeRegistry,
  getNodeStatistics as getSystemNodeStatistics,
  validateNodeRegistry
} from '../registry/index';
import {
  batchNodeRegistry,
  initializeBatchNodeRegistry,
  getBatchNodeStatistics,
  getRegistrationErrors,
  isInitialized
} from './NodeRegistry';

/**
 * 集成状态接口
 */
export interface IntegrationStatus {
  batchRegistryInitialized: boolean;
  systemRegistryInitialized: boolean;
  totalNodes: number;
  batchNodes: number;
  systemNodes: number;
  errors: string[];
  warnings: string[];
  lastUpdate: Date;
}

/**
 * 批次节点注册表集成管理器
 */
export class BatchNodeRegistryIntegration {
  private static instance: BatchNodeRegistryIntegration;
  private integrationStatus: IntegrationStatus;
  private isIntegrated: boolean = false;

  private constructor() {
    this.integrationStatus = {
      batchRegistryInitialized: false,
      systemRegistryInitialized: false,
      totalNodes: 0,
      batchNodes: 0,
      systemNodes: 0,
      errors: [],
      warnings: [],
      lastUpdate: new Date()
    };
  }

  public static getInstance(): BatchNodeRegistryIntegration {
    if (!BatchNodeRegistryIntegration.instance) {
      BatchNodeRegistryIntegration.instance = new BatchNodeRegistryIntegration();
    }
    return BatchNodeRegistryIntegration.instance;
  }

  /**
   * 完整的系统初始化
   * 按正确顺序初始化系统注册表和批次注册表
   */
  public async initializeComplete(): Promise<IntegrationStatus> {
    try {
      Debug.log('BatchNodeRegistryIntegration', '开始完整的节点注册表初始化...');

      // 1. 初始化系统注册表
      await this.initializeSystemRegistry();

      // 2. 初始化批次注册表
      await this.initializeBatchRegistry();

      // 3. 验证集成结果
      await this.validateIntegration();

      // 4. 更新状态
      this.updateIntegrationStatus();

      this.isIntegrated = true;
      Debug.log('BatchNodeRegistryIntegration', '节点注册表集成完成');

      return this.integrationStatus;

    } catch (error) {
      Debug.error('BatchNodeRegistryIntegration', '节点注册表集成失败:', error);
      this.integrationStatus.errors.push(`集成失败: ${error}`);
      throw error;
    }
  }

  /**
   * 初始化系统注册表
   */
  private async initializeSystemRegistry(): Promise<void> {
    try {
      Debug.log('BatchNodeRegistryIntegration', '初始化系统注册表...');
      await initializeVisualScriptSystem();
      this.integrationStatus.systemRegistryInitialized = true;
      Debug.log('BatchNodeRegistryIntegration', '系统注册表初始化完成');
    } catch (error) {
      const errorMsg = `系统注册表初始化失败: ${error}`;
      this.integrationStatus.errors.push(errorMsg);
      Debug.error('BatchNodeRegistryIntegration', errorMsg);
      throw error;
    }
  }

  /**
   * 初始化批次注册表
   */
  private async initializeBatchRegistry(): Promise<void> {
    try {
      Debug.log('BatchNodeRegistryIntegration', '初始化批次注册表...');
      await initializeBatchNodeRegistry();
      this.integrationStatus.batchRegistryInitialized = true;
      
      // 收集批次注册错误
      const batchErrors = getRegistrationErrors();
      if (batchErrors.length > 0) {
        this.integrationStatus.warnings.push(...batchErrors);
        Debug.warn('BatchNodeRegistryIntegration', `批次注册过程中发现 ${batchErrors.length} 个警告`);
      }
      
      Debug.log('BatchNodeRegistryIntegration', '批次注册表初始化完成');
    } catch (error) {
      const errorMsg = `批次注册表初始化失败: ${error}`;
      this.integrationStatus.errors.push(errorMsg);
      Debug.error('BatchNodeRegistryIntegration', errorMsg);
      throw error;
    }
  }

  /**
   * 验证集成结果
   */
  private async validateIntegration(): Promise<void> {
    try {
      Debug.log('BatchNodeRegistryIntegration', '验证节点注册表集成...');
      
      // 验证系统注册表
      const validation = validateNodeRegistry();
      if (!validation.isValid) {
        this.integrationStatus.errors.push(...validation.errors);
        Debug.error('BatchNodeRegistryIntegration', '系统注册表验证失败:', validation.errors);
      }
      
      if (validation.warnings.length > 0) {
        this.integrationStatus.warnings.push(...validation.warnings);
        Debug.warn('BatchNodeRegistryIntegration', '系统注册表验证警告:', validation.warnings);
      }

      Debug.log('BatchNodeRegistryIntegration', '节点注册表集成验证完成');
    } catch (error) {
      const errorMsg = `集成验证失败: ${error}`;
      this.integrationStatus.errors.push(errorMsg);
      Debug.error('BatchNodeRegistryIntegration', errorMsg);
    }
  }

  /**
   * 更新集成状态
   */
  private updateIntegrationStatus(): void {
    try {
      // 获取系统统计信息
      const systemStats = getSystemNodeStatistics();
      this.integrationStatus.systemNodes = systemStats.totalNodes;

      // 获取批次统计信息
      const batchStats = getBatchNodeStatistics();
      this.integrationStatus.batchNodes = batchStats.totalNodes;

      // 计算总节点数
      this.integrationStatus.totalNodes = this.integrationStatus.systemNodes;

      // 更新时间戳
      this.integrationStatus.lastUpdate = new Date();

      Debug.log('BatchNodeRegistryIntegration', `状态更新完成:
        - 系统节点: ${this.integrationStatus.systemNodes}
        - 批次节点: ${this.integrationStatus.batchNodes}
        - 总节点数: ${this.integrationStatus.totalNodes}
        - 错误数: ${this.integrationStatus.errors.length}
        - 警告数: ${this.integrationStatus.warnings.length}`);

    } catch (error) {
      Debug.error('BatchNodeRegistryIntegration', '状态更新失败:', error);
    }
  }

  /**
   * 获取集成状态
   */
  public getIntegrationStatus(): IntegrationStatus {
    return { ...this.integrationStatus };
  }

  /**
   * 检查是否已集成
   */
  public isIntegratedSuccessfully(): boolean {
    return this.isIntegrated && 
           this.integrationStatus.batchRegistryInitialized && 
           this.integrationStatus.systemRegistryInitialized &&
           this.integrationStatus.errors.length === 0;
  }

  /**
   * 重新加载集成（用于开发时热重载）
   */
  public async reloadIntegration(): Promise<IntegrationStatus> {
    try {
      Debug.log('BatchNodeRegistryIntegration', '重新加载节点注册表集成...');
      
      // 重置状态
      this.isIntegrated = false;
      this.integrationStatus.errors = [];
      this.integrationStatus.warnings = [];
      
      // 清空批次注册表错误
      batchNodeRegistry.clearRegistrationErrors();
      
      // 重新初始化
      return await this.initializeComplete();
      
    } catch (error) {
      Debug.error('BatchNodeRegistryIntegration', '重新加载失败:', error);
      throw error;
    }
  }

  /**
   * 获取详细的节点统计报告
   */
  public getDetailedReport(): any {
    const systemStats = getSystemNodeStatistics();
    const batchStats = getBatchNodeStatistics();
    
    return {
      integration: this.integrationStatus,
      system: systemStats,
      batch: batchStats,
      summary: {
        isHealthy: this.isIntegratedSuccessfully(),
        totalErrors: this.integrationStatus.errors.length,
        totalWarnings: this.integrationStatus.warnings.length,
        nodesCoverage: {
          system: systemStats.totalNodes,
          batch: batchStats.totalNodes,
          total: this.integrationStatus.totalNodes
        }
      }
    };
  }
}

/**
 * 导出单例实例
 */
export const batchNodeRegistryIntegration = BatchNodeRegistryIntegration.getInstance();

/**
 * 便捷函数：完整初始化
 */
export async function initializeCompleteNodeRegistry(): Promise<IntegrationStatus> {
  return batchNodeRegistryIntegration.initializeComplete();
}

/**
 * 便捷函数：获取集成状态
 */
export function getIntegrationStatus(): IntegrationStatus {
  return batchNodeRegistryIntegration.getIntegrationStatus();
}

/**
 * 便捷函数：获取详细报告
 */
export function getDetailedIntegrationReport(): any {
  return batchNodeRegistryIntegration.getDetailedReport();
}
