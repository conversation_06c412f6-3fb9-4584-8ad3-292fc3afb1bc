/**
 * 视觉脚本节点注册表
 * 批次1.7、批次2.1和批次3.2节点注册
 * 注册所有新开发的动画系统增强节点、用户服务节点和边缘计算节点
 *
 * 改进版本：
 * - 集成到统一的系统注册表
 * - 统一节点信息格式
 * - 完善错误处理和验证机制
 * - 建立清晰的初始化流程
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import {
  NodeRegistry as SystemNodeRegistry,
  NodeCategory,
  createNodeInfo,
  NodeInfo
} from '../registry/NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入批次1.7动画系统增强节点
import { 
  AnimationBlendTreeNode,
  AnimationStateMachineNode,
  IKSystemNode,
  AnimationRetargetingNode,
  AnimationCompressionNode,
  AnimationOptimizationNode
} from './animation/AnimationNodes';

import {
  AnimationBakingNode,
  AnimationExportNode,
  AnimationImportNode,
  AnimationValidationNode
} from './animation/AnimationToolNodes';

// 导入批次2.1用户服务节点
import {
  UserAuthenticationNode,
  UserRegistrationNode
} from './user/UserServiceNodes';

import {
  UserProfileNode
} from './user/UserServiceNodes';

import {
  UserPermissionNode,
  UserRoleNode
} from './user/UserServiceNodes2';

import {
  UserSessionNode,
  UserPreferencesNode
} from './user/UserServiceNodes3';

import {
  UserActivityNode,
  UserAnalyticsNode
} from './user/UserServiceNodes4';

import {
  UserNotificationNode,
  UserGroupNode,
  UserSyncNode
} from './user/UserServiceNodes5';

// 导入批次3.2边缘路由节点
import {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode,
  EdgeCompressionNode
} from './edge/EdgeRoutingNodes';

import {
  EdgeOptimizationNode,
  EdgeQoSNode
} from './edge/EdgeRoutingNodes2';

// 导入批次3.2云边协调节点
import {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from './edge/CloudEdgeNodes';

import {
  DataSynchronizationNode,
  TaskDistributionNode
} from './edge/CloudEdgeNodes2';

import {
  ResourceOptimizationNode,
  LatencyOptimizationNode
} from './edge/CloudEdgeNodes3';

import {
  BandwidthOptimizationNode,
  CostOptimizationNode
} from './edge/CloudEdgeNodes4';

// 导入批次3.2 5G网络节点
import {
  FiveGConnectionNode,
  FiveGSlicingNode,
  FiveGQoSNode
} from './edge/FiveGNetworkNodes';

import {
  FiveGLatencyNode,
  FiveGBandwidthNode
} from './edge/FiveGNetworkNodes2';

import {
  FiveGSecurityNode,
  FiveGMonitoringNode,
  FiveGOptimizationNode
} from './edge/FiveGNetworkNodes3';

/**
 * 批次节点注册表类
 * 专门负责批次1.7、2.1和3.2节点的注册和管理
 * 集成到统一的系统注册表中
 */
export class BatchNodeRegistry {
  private static instance: BatchNodeRegistry;
  private nodeTypes: Map<string, any> = new Map();
  private nodeCategories: Map<string, string[]> = new Map();
  private systemRegistry: typeof SystemNodeRegistry;
  private _isInitialized: boolean = false;
  private registrationErrors: string[] = [];

  private constructor() {
    this.systemRegistry = SystemNodeRegistry;
  }

  public static getInstance(): BatchNodeRegistry {
    if (!BatchNodeRegistry.instance) {
      BatchNodeRegistry.instance = new BatchNodeRegistry();
    }
    return BatchNodeRegistry.instance;
  }

  /**
   * 初始化并注册所有批次节点
   */
  public async initialize(): Promise<void> {
    if (this._isInitialized) {
      Debug.warn('BatchNodeRegistry', '批次节点注册表已经初始化');
      return;
    }

    try {
      Debug.log('BatchNodeRegistry', '开始初始化批次节点注册表...');

      // 清空错误记录
      this.registrationErrors = [];

      // 注册批次1.7动画系统增强节点
      await this.registerAnimationNodes();

      // 注册批次2.1用户服务节点
      await this.registerUserServiceNodes();

      // 注册批次3.2边缘计算节点
      await this.registerEdgeComputingNodes();

      // 注册批次0.2边缘计算节点（通过专门的注册表）
      await this.initializeEdgeComputingNodesRegistry();

      this._isInitialized = true;

      Debug.log('BatchNodeRegistry', `批次节点注册完成，共注册 ${this.nodeTypes.size} 个节点`);

      if (this.registrationErrors.length > 0) {
        Debug.warn('BatchNodeRegistry', `注册过程中发现 ${this.registrationErrors.length} 个错误:`, this.registrationErrors);
      }

    } catch (error) {
      Debug.error('BatchNodeRegistry', '批次节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册动画系统增强节点
   */
  private async registerAnimationNodes(): Promise<void> {
    try {
      const animationNodes = [
        AnimationBlendTreeNode,
        AnimationStateMachineNode,
        IKSystemNode,
        AnimationRetargetingNode,
        AnimationCompressionNode,
        AnimationOptimizationNode,
        AnimationBakingNode,
        AnimationExportNode,
        AnimationImportNode,
        AnimationValidationNode
      ];

      const animationCategory = 'Animation/Advanced';
      this.nodeCategories.set(animationCategory, []);

      let registeredCount = 0;

      for (const NodeClass of animationNodes) {
        try {
          // 检查节点类是否有必要的属性
          if (!NodeClass.TYPE || !NodeClass.NAME) {
            this.registrationErrors.push(`动画节点 ${NodeClass.name} 缺少必要的 TYPE 或 NAME 属性`);
            continue;
          }

          // 注册到本地注册表
          this.nodeTypes.set(NodeClass.TYPE, NodeClass);
          this.nodeCategories.get(animationCategory)?.push(NodeClass.TYPE);

          // 创建节点信息并注册到系统注册表
          const nodeInfo = createNodeInfo(
            NodeClass.TYPE,
            NodeClass.NAME,
            NodeClass.DESCRIPTION || '动画系统增强节点',
            NodeCategory.ADVANCED_ANIMATION,
            NodeClass,
            {
              icon: 'animation',
              color: '#FF6B6B',
              tags: ['animation', 'advanced', 'batch1.7'],
              experimental: false
            }
          );

          this.systemRegistry.registerNode(nodeInfo);
          registeredCount++;

        } catch (error) {
          const errorMsg = `注册动画节点 ${NodeClass.name} 失败: ${error}`;
          this.registrationErrors.push(errorMsg);
          Debug.error('BatchNodeRegistry', errorMsg);
        }
      }

      Debug.log('BatchNodeRegistry', `已注册 ${registeredCount} 个动画系统增强节点`);

    } catch (error) {
      const errorMsg = `动画节点批量注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('BatchNodeRegistry', errorMsg);
      throw error;
    }
  }

  /**
   * 注册用户服务节点
   */
  private async registerUserServiceNodes(): Promise<void> {
    try {
      const userServiceNodes = [
        UserAuthenticationNode,
        UserRegistrationNode,
        UserProfileNode,
        UserPermissionNode,
        UserRoleNode,
        UserSessionNode,
        UserPreferencesNode,
        UserActivityNode,
        UserAnalyticsNode,
        UserNotificationNode,
        UserGroupNode,
        UserSyncNode
      ];

      const userServiceCategory = 'User/Services';
      this.nodeCategories.set(userServiceCategory, []);

      let registeredCount = 0;

      for (const NodeClass of userServiceNodes) {
        try {
          // 检查节点类是否有必要的属性
          if (!NodeClass.TYPE || !NodeClass.NAME) {
            this.registrationErrors.push(`用户服务节点 ${NodeClass.name} 缺少必要的 TYPE 或 NAME 属性`);
            continue;
          }

          // 注册到本地注册表
          this.nodeTypes.set(NodeClass.TYPE, NodeClass);
          this.nodeCategories.get(userServiceCategory)?.push(NodeClass.TYPE);

          // 创建节点信息并注册到系统注册表
          const nodeInfo = createNodeInfo(
            NodeClass.TYPE,
            NodeClass.NAME,
            NodeClass.DESCRIPTION || '用户服务节点',
            NodeCategory.AUTH,
            NodeClass,
            {
              icon: 'user',
              color: '#4ECDC4',
              tags: ['user', 'service', 'batch2.1'],
              experimental: false
            }
          );

          this.systemRegistry.registerNode(nodeInfo);
          registeredCount++;

        } catch (error) {
          const errorMsg = `注册用户服务节点 ${NodeClass.name} 失败: ${error}`;
          this.registrationErrors.push(errorMsg);
          Debug.error('BatchNodeRegistry', errorMsg);
        }
      }

      Debug.log('BatchNodeRegistry', `已注册 ${registeredCount} 个用户服务节点`);

    } catch (error) {
      const errorMsg = `用户服务节点批量注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('BatchNodeRegistry', errorMsg);
      throw error;
    }
  }

  /**
   * 注册边缘计算节点
   */
  private async registerEdgeComputingNodes(): Promise<void> {
    try {
      // 边缘路由节点
      const edgeRoutingNodes = [
        EdgeRoutingNode,
        EdgeLoadBalancingNode,
        EdgeCachingNode,
        EdgeCompressionNode,
        EdgeOptimizationNode,
        EdgeQoSNode
      ];

      await this.registerNodeGroup(
        edgeRoutingNodes,
        'Edge/Routing',
        NodeCategory.EDGE_COMPUTING,
        {
          icon: 'network',
          color: '#45B7D1',
          tags: ['edge', 'routing', 'batch3.2']
        }
      );

      // 云边协调节点
      const cloudEdgeNodes = [
        CloudEdgeOrchestrationNode,
        HybridComputingNode,
        DataSynchronizationNode,
        TaskDistributionNode,
        ResourceOptimizationNode,
        LatencyOptimizationNode,
        BandwidthOptimizationNode,
        CostOptimizationNode
      ];

      await this.registerNodeGroup(
        cloudEdgeNodes,
        'Edge/CloudEdge',
        NodeCategory.EDGE_COMPUTING,
        {
          icon: 'cloud',
          color: '#96CEB4',
          tags: ['edge', 'cloud', 'batch3.2']
        }
      );

      // 5G网络节点
      const fiveGNodes = [
        FiveGConnectionNode,
        FiveGSlicingNode,
        FiveGQoSNode,
        FiveGLatencyNode,
        FiveGBandwidthNode,
        FiveGSecurityNode,
        FiveGMonitoringNode,
        FiveGOptimizationNode
      ];

      await this.registerNodeGroup(
        fiveGNodes,
        'Edge/5G',
        NodeCategory.EDGE_COMPUTING,
        {
          icon: '5g',
          color: '#FFEAA7',
          tags: ['edge', '5g', 'batch3.2']
        }
      );

      Debug.log('BatchNodeRegistry', '边缘计算节点注册完成');

    } catch (error) {
      const errorMsg = `边缘计算节点批量注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('BatchNodeRegistry', errorMsg);
      throw error;
    }
  }

  /**
   * 注册节点组的辅助方法
   */
  private async registerNodeGroup(
    nodeClasses: any[],
    categoryName: string,
    systemCategory: NodeCategory,
    options: {
      icon: string;
      color: string;
      tags: string[];
    }
  ): Promise<void> {
    this.nodeCategories.set(categoryName, []);
    let registeredCount = 0;

    for (const NodeClass of nodeClasses) {
      try {
        // 检查节点类是否有必要的属性
        if (!NodeClass.TYPE || !NodeClass.NAME) {
          this.registrationErrors.push(`节点 ${NodeClass.name} 缺少必要的 TYPE 或 NAME 属性`);
          continue;
        }

        // 注册到本地注册表
        this.nodeTypes.set(NodeClass.TYPE, NodeClass);
        this.nodeCategories.get(categoryName)?.push(NodeClass.TYPE);

        // 创建节点信息并注册到系统注册表
        const nodeInfo = createNodeInfo(
          NodeClass.TYPE,
          NodeClass.NAME,
          NodeClass.DESCRIPTION || `${categoryName}节点`,
          systemCategory,
          NodeClass,
          {
            icon: options.icon,
            color: options.color,
            tags: options.tags,
            experimental: false
          }
        );

        this.systemRegistry.registerNode(nodeInfo);
        registeredCount++;

      } catch (error) {
        const errorMsg = `注册节点 ${NodeClass.name} 失败: ${error}`;
        this.registrationErrors.push(errorMsg);
        Debug.error('BatchNodeRegistry', errorMsg);
      }
    }

    Debug.log('BatchNodeRegistry', `已注册 ${registeredCount} 个 ${categoryName} 节点`);
  }

  /**
   * 获取注册错误列表
   */
  public getRegistrationErrors(): string[] {
    return [...this.registrationErrors];
  }

  /**
   * 检查是否已初始化
   */
  public isInitialized(): boolean {
    return this._isInitialized;
  }

  /**
   * 清空注册错误
   */
  public clearRegistrationErrors(): void {
    this.registrationErrors = [];
  }

  /**
   * 初始化边缘计算节点注册表
   */
  private async initializeEdgeComputingNodesRegistry(): Promise<void> {
    try {
      // 动态导入边缘计算节点注册表
      const module = await import('../registry/EdgeComputingNodesRegistry');
      const { edgeComputingNodesRegistry } = module;
      Debug.log('BatchNodeRegistry', '边缘计算节点注册表已初始化');
      Debug.log('BatchNodeRegistry', `新增边缘计算节点: ${edgeComputingNodesRegistry.getRegisteredNodeCount()}个`);
    } catch (error) {
      const errorMsg = `边缘计算节点注册表初始化失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.warn('BatchNodeRegistry', errorMsg);
    }
  }

  /**
   * 创建节点实例
   */
  public createNode(nodeType: string, id?: string): VisualScriptNode | null {
    const NodeClass = this.nodeTypes.get(nodeType);
    if (NodeClass) {
      return new NodeClass(nodeType, NodeClass.NAME, id);
    }
    return null;
  }

  /**
   * 获取所有注册的节点类型
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.nodeTypes.keys());
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, string[]> {
    return new Map(this.nodeCategories);
  }

  /**
   * 获取指定分类的节点
   */
  public getNodesByCategory(category: string): string[] {
    return this.nodeCategories.get(category) || [];
  }

  /**
   * 检查节点类型是否已注册
   */
  public isNodeTypeRegistered(nodeType: string): boolean {
    return this.nodeTypes.has(nodeType);
  }

  /**
   * 注册单个节点
   */
  public registerNode(NodeClass: any, category: string): void {
    // 注册节点类型
    this.nodeTypes.set(NodeClass.TYPE, NodeClass);

    // 确保分类存在
    if (!this.nodeCategories.has(category)) {
      this.nodeCategories.set(category, []);
    }

    // 添加到分类
    const categoryNodes = this.nodeCategories.get(category);
    if (categoryNodes && !categoryNodes.includes(NodeClass.TYPE)) {
      categoryNodes.push(NodeClass.TYPE);
    }

    console.log(`已注册节点: ${NodeClass.TYPE} -> ${category}`);
  }

  /**
   * 获取节点信息
   */
  public getNodeInfo(nodeType: string): any {
    const NodeClass = this.nodeTypes.get(nodeType);
    if (NodeClass) {
      return {
        type: NodeClass.TYPE,
        name: NodeClass.NAME,
        description: NodeClass.DESCRIPTION || '无描述'
      };
    }
    return null;
  }

  /**
   * 获取所有节点信息
   */
  public getAllNodeInfo(): any[] {
    const nodeInfos: any[] = [];
    
    for (const [, NodeClass] of this.nodeTypes) {
      nodeInfos.push({
        type: NodeClass.TYPE,
        name: NodeClass.NAME,
        description: NodeClass.DESCRIPTION || '无描述'
      });
    }
    
    return nodeInfos;
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStatistics(): any {
    const totalNodes = this.nodeTypes.size;
    const categories = this.nodeCategories.size;
    const categoryStats: any = {};

    for (const [category, nodes] of this.nodeCategories) {
      categoryStats[category] = nodes.length;
    }

    return {
      totalNodes,
      categories,
      categoryStats,
      registeredAt: new Date().toISOString()
    };
  }
}

/**
 * 导出单例实例
 */
export const batchNodeRegistry = BatchNodeRegistry.getInstance();

/**
 * 便捷函数：初始化批次节点注册表
 */
export async function initializeBatchNodeRegistry(): Promise<void> {
  return batchNodeRegistry.initialize();
}

/**
 * 便捷函数：创建节点
 */
export function createNode(nodeType: string, id?: string): VisualScriptNode | null {
  return batchNodeRegistry.createNode(nodeType, id);
}

/**
 * 便捷函数：获取所有节点类型
 */
export function getRegisteredNodeTypes(): string[] {
  return batchNodeRegistry.getRegisteredNodeTypes();
}

/**
 * 便捷函数：获取节点分类
 */
export function getNodeCategories(): Map<string, string[]> {
  return batchNodeRegistry.getNodeCategories();
}

/**
 * 便捷函数：获取节点信息
 */
export function getNodeInfo(nodeType: string): any {
  return batchNodeRegistry.getNodeInfo(nodeType);
}

/**
 * 便捷函数：获取所有节点信息
 */
export function getAllNodeInfo(): any[] {
  return batchNodeRegistry.getAllNodeInfo();
}

/**
 * 便捷函数：获取统计信息
 */
export function getBatchNodeStatistics(): any {
  return batchNodeRegistry.getNodeStatistics();
}

/**
 * 便捷函数：获取注册错误
 */
export function getRegistrationErrors(): string[] {
  return batchNodeRegistry.getRegistrationErrors();
}

/**
 * 便捷函数：检查是否已初始化
 */
export function isInitialized(): boolean {
  return batchNodeRegistry.isInitialized();
}

// 初始化节点注册表
console.log('DL引擎视觉脚本节点注册表已初始化');
console.log('批次1.7动画系统增强节点：10个');
console.log('批次2.1用户服务节点：12个');
console.log('批次3.2边缘计算节点：24个');
console.log('  - 边缘路由节点：6个');
console.log('  - 云边协调节点：8个');
console.log('  - 5G网络节点：8个');
console.log('总计新增节点：46个');

const stats = getBatchNodeStatistics();
console.log('节点统计信息：', stats);
