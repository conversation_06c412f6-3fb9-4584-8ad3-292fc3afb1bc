# 视觉脚本节点注册系统

## 概述

视觉脚本节点注册系统是DL引擎的核心组件之一，负责管理所有可用的视觉脚本节点。该系统提供了统一的节点注册、查找、创建和管理功能。

## 主要特性

- **统一管理**: 集中管理所有节点类型和元数据
- **分类组织**: 按功能分类组织节点，便于查找和使用
- **动态加载**: 支持动态导入节点类，避免循环依赖
- **类型安全**: 完整的TypeScript类型支持
- **热重载**: 支持开发时的热重载功能
- **验证机制**: 内置节点注册表完整性验证
- **统计信息**: 提供详细的节点统计和状态信息

## 核心组件

### 1. NodeRegistry
节点注册表的核心管理器，采用单例模式。

```typescript
import { NodeRegistry } from './NodeRegistry';

// 获取节点信息
const nodeInfo = NodeRegistry.getNode('CreateEntity');

// 创建节点实例
const node = NodeRegistry.createNode('CreateEntity', '创建实体');

// 搜索节点
const searchResults = NodeRegistry.searchNodes('实体');

// 按分类获取节点
const entityNodes = NodeRegistry.getNodesByCategory(NodeCategory.ENTITY_MANAGEMENT);
```

### 2. NodeRegistrations
具体的节点注册实现，负责导入和注册所有可用的节点类。

```typescript
import { registerAllAvailableNodes } from './NodeRegistrations';

// 注册所有可用节点
await registerAllAvailableNodes();
```

### 3. 系统初始化
统一的系统初始化入口。

```typescript
import { initializeVisualScriptSystem } from './index';

// 初始化整个视觉脚本系统
await initializeVisualScriptSystem();
```

## 节点分类

系统支持以下节点分类：

### 核心系统
- `CORE`: 核心功能节点
- `ENTITY_MANAGEMENT`: 实体管理
- `COMPONENT_MANAGEMENT`: 组件管理
- `TRANSFORM`: 变换操作

### 渲染系统
- `RENDERING`: 基础渲染
- `MATERIAL`: 材质系统
- `LIGHTING`: 光照系统
- `CAMERA`: 相机管理
- `POST_PROCESSING`: 后处理效果

### 物理系统
- `PHYSICS`: 基础物理
- `SOFT_BODY`: 软体物理

### 动画系统
- `ANIMATION`: 基础动画
- `ADVANCED_ANIMATION`: 高级动画

### 音频系统
- `AUDIO`: 基础音频
- `ADVANCED_AUDIO`: 高级音频

### 输入系统
- `INPUT`: 基础输入
- `ADVANCED_INPUT`: 高级输入
- `SENSOR_INPUT`: 传感器输入
- `VR_AR_INPUT`: VR/AR输入

### 网络系统
- `NETWORK`: 网络通信

### AI系统
- `AI`: 基础AI
- `COMPUTER_VISION`: 计算机视觉
- `NLP`: 自然语言处理
- `MACHINE_LEARNING`: 机器学习

### 场景系统
- `SCENE`: 场景管理
- `SCENE_EDITING`: 场景编辑
- `TERRAIN`: 地形系统
- `WATER`: 水体系统
- `PARTICLES`: 粒子系统

### 资源系统
- `RESOURCES`: 资源管理

### 工业系统
- `INDUSTRIAL`: 工业自动化
- `MES`: 制造执行系统
- `QUALITY`: 质量管理
- `MAINTENANCE`: 预测性维护

### 边缘计算
- `EDGE_COMPUTING`: 边缘计算

### 专业应用
- `BLOCKCHAIN`: 区块链
- `LEARNING_RECORD`: 学习记录
- `RAG_APPLICATION`: RAG应用
- `SPATIAL_INFO`: 空间信息
- `DIGITAL_HUMAN`: 数字人生成

### 项目管理
- `PROJECT`: 项目管理
- `COLLABORATION`: 协作功能

### 数据服务
- `DATA`: 数据服务
- `FILE`: 文件服务
- `AUTH`: 认证授权

### UI系统
- `UI`: 用户界面

### 动作捕捉
- `MOTION_CAPTURE`: 动作捕捉

## 使用示例

### 基本使用

```typescript
import { initializeVisualScriptSystem, NodeRegistry, NodeCategory } from './index';

// 1. 初始化系统
await initializeVisualScriptSystem();

// 2. 创建节点
const createEntityNode = NodeRegistry.createNode('CreateEntity');

// 3. 搜索节点
const searchResults = NodeRegistry.searchNodes('实体');

// 4. 按分类获取节点
const renderingNodes = NodeRegistry.getNodesByCategory(NodeCategory.RENDERING);
```

### 添加新节点

```typescript
import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { MyCustomNode } from './MyCustomNode';

// 创建节点信息
const nodeInfo = createNodeInfo(
  'MyCustom',
  '我的自定义节点',
  '这是一个自定义节点的示例',
  NodeCategory.CORE,
  MyCustomNode,
  {
    icon: 'star',
    color: '#FFD700',
    tags: ['custom', 'example']
  }
);

// 注册节点
NodeRegistry.registerNode(nodeInfo);
```

### 获取统计信息

```typescript
import { getNodeStatistics, getSystemStatus } from './index';

// 获取详细统计
const stats = getNodeStatistics();
console.log(`总节点数: ${stats.totalNodes}`);
console.log(`分类数: ${stats.categoryCounts.size}`);

// 获取系统状态
const status = getSystemStatus();
console.log(`系统已初始化: ${status.isInitialized}`);
```

### 开发时热重载

```typescript
import { reloadNodeRegistry } from './index';

// 重新加载节点注册表
await reloadNodeRegistry();
```

## 错误处理

系统提供了完整的错误处理和验证机制：

```typescript
import { validateNodeRegistry } from './NodeRegistry';

// 验证注册表完整性
const validation = validateNodeRegistry();
if (!validation.isValid) {
  console.error('验证失败:', validation.errors);
  console.warn('警告:', validation.warnings);
}
```

## 注意事项

1. **初始化顺序**: 必须先调用 `initializeVisualScriptSystem()` 才能使用其他功能
2. **异步操作**: 节点注册过程是异步的，需要使用 `await`
3. **错误处理**: 建议在生产环境中添加适当的错误处理
4. **性能考虑**: 大量节点的注册可能需要一些时间，建议在应用启动时进行
5. **热重载**: 热重载功能主要用于开发环境，生产环境中应谨慎使用

## 扩展开发

要添加新的节点类型：

1. 创建节点类，继承自 `VisualScriptNode`
2. 在 `NodeRegistrations.ts` 中添加注册逻辑
3. 更新相应的分类和导入语句
4. 测试节点的创建和功能

系统设计为可扩展的，支持轻松添加新的节点类型和分类。
